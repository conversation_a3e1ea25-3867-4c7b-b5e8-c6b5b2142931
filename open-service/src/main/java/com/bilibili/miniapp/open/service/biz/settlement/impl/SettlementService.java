package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSettlementDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.accrual.IAccrualService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.SettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiUploadResult;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算服务实现
 * 金额单位为分保留2位小数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class SettlementService implements ISettlementService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppOpenSettlementDao settlementDao;

    @Autowired
    private IFinanceService financeService;

    @Autowired
    private IBFSService bfsService;

    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private IAccrualService accrualService;

    /**
     * 固定比例：(1+6%)，用作计算金额
     */
    private final BigDecimal fixedProportionRatio = new BigDecimal("0.06").add(BigDecimal.ONE);

    @Override
    public SettlementDateListBo getSettlementDates(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        List<MiniAppOpenAccrualPo> accrualList = accrualService.queryAccruals(appId, WithdrawStatus.WITHDRAWABLE);

        List<SettlementDateBo> settlementList = accrualList.stream()
                .map(po -> SettlementDateBo.builder()
                        .date(po.getIncomeDate())
                        .accrualId(po.getAccrualId())
                        .build())
                .collect(Collectors.toList());

        return SettlementDateListBo.builder()
                .settlementList(settlementList)
                .build();
    }

    @Override
    public SettlementPreviewBo getSettlementPreview(Long mid, String appId, List<String> accrualIds) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        if (CollectionUtils.isEmpty(accrualIds)) {
            return SettlementPreviewBo.emptyInstance();
        }


        List<MiniAppOpenAccrualPo> accrualList = accrualService.queryAccruals(appId, accrualIds);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");

        BigDecimal withdrawApplyAmount = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        BigDecimal taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());

        BigDecimal actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        return SettlementPreviewBo.builder()
                .withdrawApplyAmount(withdrawApplyAmount)
                .taxFee(taxFee)
                .actualWithdrawAmount(actualWithdrawAmount)
                .build();
    }

    /**
     * 由于可支持的文件类型比较多，需要解析文件类型映射到http media type的逻辑会比较复杂，直接使用bfs url返回的类型
     */
    @Override
    public InvoiceUploadBo uploadInvoice(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            BFSUploadResult bfsUploadResult = bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN, file.getOriginalFilename(), fileBytes);
            log.info("BFS上传成功，url: {}", bfsUploadResult.getUrl());

            AssertUtil.isTrue(StringUtils.isNotBlank(bfsUploadResult.getUrl()), ErrorCodeType.NO_DATA.getCode(), "上传后未返回图片url");

            String partName = getBfsFileNameFromUrl(bfsUploadResult.getUrl());

            HuilianyiUploadResult huilianyiResult = huilianyiPaymentService.uploadAttachments(bfsUploadResult.getUrl(), partName);
            log.info("汇联易上传成功，oid: {}", huilianyiResult.getOid());

            return InvoiceUploadBo.builder()
                    .oid(huilianyiResult.getOid())
                    .url(bfsUploadResult.getUrl())
                    .build();

        } catch (Exception e) {
            log.error("发票上传失败", e);
            throw new ServiceException("发票上传失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<SettlementItemBo> getSettlementList(Long mid,
                                                          String appId,
                                                          Integer page,
                                                          Integer size,
                                                          Timestamp beginTime,
                                                          Timestamp endTime,
                                                          Integer settlementStatus) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        MiniAppOpenSettlementPoExample.Criteria criteria = example.createCriteria();

        criteria.andAppIdEqualTo(appId);

        if (beginTime != null) {
            criteria.andSettlementBeginTimeGreaterThanOrEqualTo(beginTime);
        }

        if (endTime != null) {
            criteria.andSettlementEndTimeLessThanOrEqualTo(endTime);
        }

        if (settlementStatus != null) {
            criteria.andSettlementStatusEqualTo(settlementStatus);
        }

        criteria.andIsDeletedEqualTo(0);

        long total = settlementDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        Page pageInfo = Page.valueOf(page, size);
        example.setLimit(pageInfo.getLimit());
        example.setOffset(pageInfo.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);


        List<SettlementItemBo> settlementItemList = settlementList.stream()
                .map(this::convertToSettlementItemBo)
                .collect(Collectors.toList());

        return new PageResult<>((int) total, settlementItemList);
    }

    @Override
    public SettlementDetailBo getSettlementDetail(Long mid, String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(settlementList), ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        MiniAppOpenSettlementPo settlement = settlementList.get(0);

        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        CompanyDetailBo detail = companyService.getDetail(mid);

        return SettlementDetailBo.builder()
                .settlementId(settlement.getSettlementId())
                .beginTime(settlement.getSettlementBeginTime())
                .endTime(settlement.getSettlementEndTime())
                .companyName(detail.getCompanyInfo().getCompanyName())
                .actualWithdrawAmount(settlement.getActualWithdrawAmount())
                .settlementStatus(settlement.getSettlementStatus())
                .paymentOrderId(settlement.getPaymentOrderId())
                .build();
    }

    private SettlementItemBo convertToSettlementItemBo(MiniAppOpenSettlementPo po) {
        return SettlementItemBo.builder()
                .settlementId(po.getSettlementId())
                .appId(po.getAppId())
                .settlementBeginTime(po.getSettlementBeginTime())
                .settlementEndTime(po.getSettlementEndTime())
                .settlementStatus(po.getSettlementStatus())
                .withdrawApplyAmount(po.getWithdrawApplyAmount())
                .actualWithdrawAmount(po.getActualWithdrawAmount())
                .taxFee(po.getTaxFee())
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .build();
    }

    private String getBfsFileNameFromUrl(String url) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).build();
        List<String> pathSegments = uriComponents.getPathSegments();
        return pathSegments.get(pathSegments.size() - 1);
    }

    /**
     * 计算税费：withdraw_apply_amount/(1+6%)*税率，进行四舍五入
     */
    private Long calculateTaxFee(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        BigDecimal result = amount
                .divide(fixedProportionRatio, 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(taxRatio));
        return result.setScale(0, RoundingMode.HALF_UP).longValue();
    }

    /**
     * 计算实际提现金额：withdraw_apply_amount/(1+6%)*(1+税率)
     */
    private Long calculateActualWithdrawAmount(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        BigDecimal result = amount
                .divide(fixedProportionRatio, 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.ONE.add(new BigDecimal(taxRatio)));
        return result.setScale(0, RoundingMode.HALF_UP).longValue();
    }

    @Override
    public void confirmSettlement(Long mid, String settlementId) {
        updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_CONFIRMATION, SettlementStatus.PENDING_UPLOAD_INVOICE);
    }

    @Override
    public void cancelSettlement(Long mid, String settlementId) {
        updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_CONFIRMATION, SettlementStatus.CANCELED);
    }

    private void updateSettlementStatus(Long mid, String settlementId, SettlementStatus sourceStatus, SettlementStatus targetStatus) {
        MiniAppOpenSettlementPo settlement = getSettlement(settlementId);
        AssertUtil.isTrue(settlement != null, ErrorCodeType.NO_DATA.getCode(), "结算单不存在");
        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);
        AssertUtil.isTrue(settlement.getSettlementStatus() == sourceStatus.getCode(), ErrorCodeType.NO_DATA.getCode(), "结算单状态不正确");

        MiniAppOpenSettlementPoExample updateExample = new MiniAppOpenSettlementPoExample();
        updateExample.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andSettlementStatusEqualTo(sourceStatus.getCode())
                .andIsDeletedEqualTo(0);

        MiniAppOpenSettlementPo updateRecord = new MiniAppOpenSettlementPo();
        updateRecord.setSettlementStatus(targetStatus.getCode());

        settlementDao.updateByExampleSelective(updateRecord, updateExample);
    }

    private MiniAppOpenSettlementPo getSettlement(String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        return settlementList.get(0);
    }

    @Override
    public InvoiceIssuanceBaseInfoBo getInvoiceBaseInfo() {
        SettlementConfig settlementConfig = configCenter.getSettlementConfig();
        return InvoiceIssuanceBaseInfoBo.builder()
                .bankCode(settlementConfig.getBankCode())
                .bankName(settlementConfig.getBankName())
                .companyName(settlementConfig.getCompanyName())
                .taxpayerIdentificationNumber(settlementConfig.getTaxpayerIdentificationNumber())
                .build();
    }

    @Override
    public void createSettlement(Long mid, SettlementCreateReqBo request) {
        // 1. 校验参数
        AssertUtil.isTrue(!CollectionUtils.isEmpty(request.getAccrualIds()), ErrorCodeType.BAD_PARAMETER.getCode(), "预提单ID列表不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInvoiceOid()), ErrorCodeType.BAD_PARAMETER.getCode(), "发票OID不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInvoiceUrl()), ErrorCodeType.BAD_PARAMETER.getCode(), "发票URL不能为空");

        // 2. 查询预提单信息
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAccrualIdIn(request.getAccrualIds())
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualList = null;
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");
        AssertUtil.isTrue(accrualList.size() == request.getAccrualIds().size(), ErrorCodeType.BAD_PARAMETER.getCode(), "部分预提单不存在");

        // 3. 校验所有预提单属于同一个appId
        String appId = accrualList.get(0).getAppId();
        boolean allSameAppId = accrualList.stream().allMatch(accrual -> Objects.equals(accrual.getAppId(), appId));
        AssertUtil.isTrue(allSameAppId, ErrorCodeType.BAD_PARAMETER.getCode(), "所有预提单必须属于同一个小程序");

        // 4. 校验当前用户是否为该appId的超管
        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        // 5. 校验所有预提单的withdraw_status是否都是1可提现
        boolean allWithdrawable = accrualList.stream().allMatch(accrual ->
                Objects.equals(accrual.getWithdrawStatus(), WithdrawStatus.WITHDRAWABLE.getCode()));
        AssertUtil.isTrue(allWithdrawable, ErrorCodeType.BAD_DATA.getCode(), "所有预提单必须处于可提现状态");

        // 6. 校验预提单时间连续性
        validateAccrualDatesContinuity(accrualList);

        // 7. 获取财务信息和税率
        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null,
                ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        // 8. 计算金额
        BigDecimal withdrawApplyAmount = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());
        BigDecimal actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        // 9. 创建结算单
        createSettlementRecord(appId, accrualList, withdrawApplyAmount, taxFee, actualWithdrawAmount,
                request.getInvoiceOid(), request.getInvoiceUrl(), taxType.getTaxRatio());

        // 10. 更新预提单状态为提现中
        updateAccrualStatus(request.getAccrualIds(), WithdrawStatus.WITHDRAWING.getCode());
    }

    /**
     * 校验预提单时间连续性
     */
    private void validateAccrualDatesContinuity(List<MiniAppOpenAccrualPo> accrualList) {
        // 按日期排序
        List<String> sortedDates = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        // 校验日期连续性
        for (int i = 1; i < sortedDates.size(); i++) {
            String prevDate = sortedDates.get(i - 1);
            String currDate = sortedDates.get(i);

            try {
                LocalDate prevLocalDate = LocalDate.parse(prevDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate currLocalDate = LocalDate.parse(currDate, DateTimeFormatter.ofPattern("yyyyMMdd"));

                // 检查是否连续（相差1天）
                if (!currLocalDate.equals(prevLocalDate.plusDays(1))) {
                    throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(),
                            String.format("预提单日期不连续，%s 和 %s 之间有间隔", prevDate, currDate));
                }
            } catch (DateTimeParseException e) {
                throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "预提单日期格式错误");
            }
        }
    }

    /**
     * 创建结算单记录
     */
    private void createSettlementRecord(String appId, List<MiniAppOpenAccrualPo> accrualList,
                                      BigDecimal withdrawApplyAmount, BigDecimal taxFee, BigDecimal actualWithdrawAmount,
                                      String invoiceOid, String invoiceUrl, String taxRatio) {

        // 计算结算时间范围
        List<String> sortedDates = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        String beginDate = sortedDates.get(0);
        String endDate = sortedDates.get(sortedDates.size() - 1);

        Timestamp settlementBeginTime = parseIncomeDate(beginDate);
        Timestamp settlementEndTime = parseIncomeDate(endDate);

        // 生成结算单ID
        String settlementId = generateSettlementId();

        // 创建SettlementExtraBo
        SettlementExtraBo settlementExtra = SettlementExtraBo.builder()
                .taxRatio(taxRatio)
                .build();

        // 创建结算单PO
        MiniAppOpenSettlementPo settlementPo = MiniAppOpenSettlementPo.builder()
                .appId(appId)
                .settlementBeginTime(settlementBeginTime)
                .settlementEndTime(settlementEndTime)
                .settlementId(settlementId)
                .settlementStatus(SettlementStatus.PENDING_CONFIRMATION.getCode())
                .withdrawApplyAmount(withdrawApplyAmount)
                .actualWithdrawAmount(actualWithdrawAmount)
                .taxFee(taxFee)
                .invoiceOid(invoiceOid)
                .invoiceUrl(invoiceUrl)
                .extra(JsonUtil.writeValueAsString(settlementExtra))
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(0)
                .build();

        // 插入数据库
        int result = settlementDao.insertSelective(settlementPo);
        AssertUtil.isTrue(result > 0, ErrorCodeType.SYSTEM_ERROR.getCode(), "创建结算单失败");

        log.info("创建结算单成功，settlementId: {}, appId: {}, withdrawApplyAmount: {}",
                settlementId, appId, withdrawApplyAmount);
    }

    /**
     * 解析收入日期字符串为Timestamp
     */
    private Timestamp parseIncomeDate(String incomeDate) {
        try {
            LocalDate date = LocalDate.parse(incomeDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            return Timestamp.valueOf(date.atStartOfDay());
        } catch (DateTimeParseException e) {
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "日期格式错误: " + incomeDate);
        }
    }

    /**
     * 生成结算单ID
     */
    private String generateSettlementId() {
        return "SETTLEMENT_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 批量更新预提单状态
     */
    private void updateAccrualStatus(List<String> accrualIds, Integer status) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAccrualIdIn(accrualIds)
                .andIsDeletedEqualTo(0);

        MiniAppOpenAccrualPo updatePo = new MiniAppOpenAccrualPo();
        updatePo.setWithdrawStatus(status);
        updatePo.setMtime(new Timestamp(System.currentTimeMillis()));

        int result = 1;
        AssertUtil.isTrue(result == accrualIds.size(), ErrorCodeType.SYSTEM_ERROR.getCode(), "更新预提单状态失败");

        log.info("批量更新预提单状态成功，accrualIds: {}, status: {}", accrualIds, status);
    }
}
