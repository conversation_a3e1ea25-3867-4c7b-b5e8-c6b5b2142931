package com.bilibili.miniapp.open.service.biz.accrual;

import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 */
public interface IAccrualService {

    List<MiniAppOpenAccrualPo> queryAccruals(String appId, WithdrawStatus withdrawStatus);

    List<MiniAppOpenAccrualPo> queryAccruals(String appId, List<String> accrualIds);

    void updateStatus(List<String> accrualIds, WithdrawStatus status);

    String createAccrual(List<MiniAppOpenIaaIncomeDetailPo> incomeDetailPos);
}
