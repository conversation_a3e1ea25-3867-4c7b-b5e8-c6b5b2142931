package com.bilibili.miniapp.open.portal.vo.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 创建结算单请求VO
 *
 * <AUTHOR>
 * @date 2025/6/6
 */
@Data
public class SettlementCreateReqVo {

    /**
     * 汇联易预提单ID列表
     */
    @JSONField(name = "accrual_ids")
    private List<String> accrualIds;

    /**
     * 发票OID
     */
    @JSONField(name = "invoice_oid")
    private String invoiceOid;

    /**
     * 发票URL
     */
    @JSONField(name = "invoice_url")
    private String invoiceUrl;
}
